import { useCallback, useEffect, useRef, useState } from 'react';

import { ChatHistoryItem, PaginatedChatHistoryResponse } from '@/types/agents';

interface UseInfiniteScrollOptions {
  pageSize?: number;
  prefetchPages?: number;
  scrollThreshold?: number;
  isServiceReady?: boolean;
}

interface UseInfiniteScrollState {
  items: ChatHistoryItem[];
  currentPage: number;
  totalItems: number;
  hasNextPage: boolean;
  isLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
}

interface UseInfiniteScrollReturn extends UseInfiniteScrollState {
  loadMore: () => Promise<void>;
  reset: () => void;
  handleScroll: (event: React.UIEvent<HTMLDivElement>) => void;
}

export const useInfiniteScroll = (
  fetchFunction: (
    page: number,
    pageSize: number
  ) => Promise<PaginatedChatHistoryResponse>,
  options: UseInfiniteScrollOptions = {}
): UseInfiniteScrollReturn => {
  const {
    pageSize = 20,
    prefetchPages = 2,
    scrollThreshold = 200,
    isServiceReady = true,
  } = options;

  const [state, setState] = useState<UseInfiniteScrollState>({
    items: [],
    currentPage: 0,
    totalItems: 0,
    hasNextPage: true,
    isLoading: false,
    isLoadingMore: false,
    error: null,
  });

  const prefetchedPages = useRef<Set<number>>(new Set());
  const isLoadingRef = useRef(false);
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);

  const loadPage = useCallback(
    async (page: number, isInitial = false) => {
      if (isLoadingRef.current || prefetchedPages.current.has(page)) {
        return;
      }

      // Don't load if we don't have next page and it's not the initial load
      if (!isInitial && !state.hasNextPage) {
        return;
      }

      isLoadingRef.current = true;
      prefetchedPages.current.add(page);

      setState(prev => ({
        ...prev,
        isLoading: isInitial,
        isLoadingMore: !isInitial,
        error: null,
      }));

      try {
        const response = await fetchFunction(page, pageSize);

        if (response.status && response.data) {
          const { items, total, page: responsePage } = response.data;

          setState(prev => {
            // For chat history, we want newest messages at the bottom
            // So we prepend older messages (higher page numbers) to the beginning
            const newItems = page === 1 ? items : [...items, ...prev.items];

            return {
              ...prev,
              items: newItems,
              currentPage: Math.max(prev.currentPage, responsePage),
              totalItems: total,
              hasNextPage: newItems.length < total,
              isLoading: false,
              isLoadingMore: false,
            };
          });
        } else {
          // If response is not successful, clear loading states
          setState(prev => ({
            ...prev,
            isLoading: false,
            isLoadingMore: false,
          }));
        }
      } catch (error) {
        console.error('Failed to load page:', error);
        setState(prev => ({
          ...prev,
          isLoading: false,
          isLoadingMore: false,
          error:
            error instanceof Error ? error.message : 'Failed to load messages',
        }));
        // Remove from prefetched pages so we can retry
        prefetchedPages.current.delete(page);
      } finally {
        isLoadingRef.current = false;
      }
    },
    [fetchFunction, pageSize, state.hasNextPage]
  );

  const loadMore = useCallback(async () => {
    if (!state.hasNextPage || isLoadingRef.current || !isServiceReady) {
      return;
    }

    const nextPage = state.currentPage + 1;
    await loadPage(nextPage);
  }, [state.hasNextPage, state.currentPage, loadPage, isServiceReady]);

  const prefetchNextPages = useCallback(async () => {
    if (!state.hasNextPage || isLoadingRef.current || !isServiceReady) {
      return;
    }

    const startPage = state.currentPage + 1;
    const endPage = Math.min(
      startPage + prefetchPages - 1,
      Math.ceil(state.totalItems / pageSize)
    );

    for (let page = startPage; page <= endPage; page++) {
      if (!prefetchedPages.current.has(page)) {
        // Don't await here to allow parallel prefetching
        loadPage(page);
      }
    }
  }, [
    state.hasNextPage,
    state.currentPage,
    state.totalItems,
    prefetchPages,
    pageSize,
    loadPage,
    isServiceReady,
  ]);

  const handleScroll = useCallback(
    (event: React.UIEvent<HTMLDivElement>) => {
      const container = event.currentTarget;
      scrollContainerRef.current = container;

      // Check if user scrolled near the top (for loading older messages)
      const { scrollTop } = container;

      if (
        scrollTop <= scrollThreshold &&
        state.hasNextPage &&
        !isLoadingRef.current &&
        isServiceReady
      ) {
        loadMore();
      }

      // Trigger prefetching when user is getting close to loaded content
      if (
        scrollTop <= scrollThreshold * 2 &&
        state.hasNextPage &&
        isServiceReady
      ) {
        prefetchNextPages();
      }
    },
    [
      scrollThreshold,
      state.hasNextPage,
      loadMore,
      prefetchNextPages,
      isServiceReady,
    ]
  );

  const reset = useCallback(() => {
    setState({
      items: [],
      currentPage: 0,
      totalItems: 0,
      hasNextPage: true,
      isLoading: false,
      isLoadingMore: false,
      error: null,
    });
    prefetchedPages.current.clear();
    isLoadingRef.current = false;
  }, []);

  // Initial load - only trigger once when component mounts and service is ready
  useEffect(() => {
    if (
      state.items.length === 0 &&
      !state.isLoading &&
      state.hasNextPage &&
      !isLoadingRef.current &&
      isServiceReady
    ) {
      // Add a delay to allow Axios instance to initialize
      const timeoutId = setTimeout(() => {
        loadPage(1, true);
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [isServiceReady, loadPage]); // Include isServiceReady and loadPage in dependencies

  return {
    ...state,
    loadMore,
    reset,
    handleScroll,
  };
};
